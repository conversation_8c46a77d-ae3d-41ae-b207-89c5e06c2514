package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	// Logger 全局日志实例
	Logger *zap.Logger
	// Sugar 全局Sugar日志实例，提供更简洁的API
	Sugar *zap.SugaredLogger
)

// Init 初始化日志系统
func Init() error {
	// 创建自定义编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    customLevelEncoder,
		EncodeTime:     customTimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   customCallerEncoder,
	}

	// 创建控制台编码器
	encoder := zapcore.NewConsoleEncoder(encoderConfig)

	// 设置日志级别
	level := getLogLevel()

	// 创建核心
	core := zapcore.NewCore(
		encoder,
		zapcore.AddSync(os.Stdout),
		level,
	)

	// 创建logger，添加调用者信息
	Logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	Sugar = Logger.Sugar()

	return nil
}

// customTimeEncoder 自定义时间编码器，格式：[2025-09-10 10:08:59]
func customTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(fmt.Sprintf("[%s]", t.Format("2006-01-02 15:04:05")))
}

// customLevelEncoder 自定义级别编码器，格式：[INFO]
func customLevelEncoder(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(fmt.Sprintf("[%s]", strings.ToUpper(level.String())))
}

// customCallerEncoder 自定义调用者编码器，格式：[filename:line]
func customCallerEncoder(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	// 获取文件名（不包含路径）
	filename := filepath.Base(caller.File)
	enc.AppendString(fmt.Sprintf("[%s:%d]", filename, caller.Line))
}

// getLogLevel 从环境变量获取日志级别
func getLogLevel() zapcore.Level {
	levelStr := strings.ToLower(os.Getenv("LOG_LEVEL"))
	switch levelStr {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn", "warning":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	case "fatal":
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

// Info 记录Info级别日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Debug 记录Debug级别日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Warn 记录Warn级别日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error 记录Error级别日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 记录Fatal级别日志并退出程序
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}

// Infof 记录Info级别日志（格式化）
func Infof(template string, args ...interface{}) {
	Sugar.Infof(template, args...)
}

// Debugf 记录Debug级别日志（格式化）
func Debugf(template string, args ...interface{}) {
	Sugar.Debugf(template, args...)
}

// Warnf 记录Warn级别日志（格式化）
func Warnf(template string, args ...interface{}) {
	Sugar.Warnf(template, args...)
}

// Errorf 记录Error级别日志（格式化）
func Errorf(template string, args ...interface{}) {
	Sugar.Errorf(template, args...)
}

// Fatalf 记录Fatal级别日志（格式化）并退出程序
func Fatalf(template string, args ...interface{}) {
	Sugar.Fatalf(template, args...)
}

// WithFields 创建带字段的日志记录器
func WithFields(fields ...zap.Field) *zap.Logger {
	return Logger.With(fields...)
}

// Sync 同步日志缓冲区
func Sync() error {
	if Logger != nil {
		return Logger.Sync()
	}
	return nil
}

// GetCaller 获取调用者信息，用于手动添加调用者信息
func GetCaller(skip int) (string, int) {
	_, file, line, ok := runtime.Caller(skip + 1)
	if !ok {
		return "unknown", 0
	}
	return filepath.Base(file), line
}
