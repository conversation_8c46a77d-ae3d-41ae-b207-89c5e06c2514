package services

import (
	"context"
	"encoding/json"
	"shovel/biz_services/account/internal/database"
	"shovel/biz_services/account/internal/logger"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"go.uber.org/zap"
)

// DaprPublisher Dapr消息发布服务
type DaprPublisher struct {
	client dapr.Client
}

// NewDaprPublisher 创建新的Dapr发布服务
func NewDaprPublisher() (*DaprPublisher, error) {
	client, err := dapr.NewClient()
	if err != nil {
		return nil, err
	}

	return &DaprPublisher{
		client: client,
	}, nil
}

// Close 关闭Dapr客户端
func (d *DaprPublisher) Close() {
	if d.client != nil {
		d.client.Close()
	}
}

// LoginEvent 登录事件消息结构
type LoginEvent struct {
	Account       string     `json:"account"`
	LoginTime     time.Time  `json:"login_time"`
	LastLoginTime *time.Time `json:"last_login_time"`
	Timestamp     int64      `json:"timestamp"`
	EventType     string     `json:"event_type"`
}

// PublishLoginEvent 发布用户登录事件
func (d *DaprPublisher) PublishLoginEvent(userAccount string, user *database.User) {
	// 准备上次登录时间
	var lastLoginTime *time.Time
	if user != nil {
		// 如果用户存在，使用数据库中的登录时间作为上次登录时间
		lastLoginTime = &user.LoginTime
	}
	// 如果用户不存在，lastLoginTime 保持为 nil

	// 创建登录事件
	currentTime := time.Now()
	event := LoginEvent{
		Account:       userAccount,
		LoginTime:     currentTime,
		LastLoginTime: lastLoginTime,
		Timestamp:     currentTime.Unix(),
		EventType:     "user_login",
	}

	// 序列化事件数据
	eventData, err := json.Marshal(event)
	if err != nil {
		logger.Error("序列化登录事件失败", zap.Error(err))
		return
	}

	// 异步发布消息，不阻塞登录流程
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err := d.client.PublishEvent(ctx, "pubsub", "user-login", eventData)
		if err != nil {
			logger.Error("发布登录事件失败", zap.Error(err))
		} else {
			if lastLoginTime != nil {
				logger.Info("成功发布登录事件",
					zap.String("user", userAccount),
					zap.String("loginTime", event.LoginTime.Format("2006-01-02 15:04:05")),
					zap.String("lastLoginTime", lastLoginTime.Format("2006-01-02 15:04:05")))
			} else {
				logger.Info("成功发布登录事件",
					zap.String("user", userAccount),
					zap.String("loginTime", event.LoginTime.Format("2006-01-02 15:04:05")),
					zap.Bool("firstLogin", true))
			}
		}
	}()
}
